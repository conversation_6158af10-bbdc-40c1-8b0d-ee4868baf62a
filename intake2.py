import pandas as pd

# 1) Load your Excel file
file_path = r"C:\Users\<USER>\OneDrive - Betterhome Group Ltd\Documents\BS\Data and Analytics\Data\Intake\Intake_2020721\intake_20250721v2.xlsx"
df = pd.read_excel(file_path)

# 2) Normalize column names (lowercase + strip spaces/underscores)
df.columns = (
    df.columns
      .str.lower()
      .str.replace(r'[\s_]+', '', regex=True)
)

# 3) Rename to our standard fields, including the two new ones
col_map = {
    'mainapplicantidnumber':     'IdNumber',
    'businessentity':            'BusinessEntity',
    'registeredinthenameof':     'RegisteredInNameOf',
    'submissiontype':            'SubmissionType',
    'statusworkflow':            'StatusWorkflow',
    'leadid':                    'LeadID',
    'product':                   'LeadType',
    'processingcomment':         'LeadProcessComment',
    'leadexclusionruleid':       'LeadExclusionRuleId',
    'status':                    'Status',
    'intakedate':                'LeadIntakeDate',
    'bankname':                  'ApplicantBank'
}
df = df.rename(columns=col_map)

# 4) Sanity-check mapping
missing = set(col_map.values()) - set(df.columns)
if missing:
    raise KeyError(f"Missing after rename: {missing}\nAvailable: {df.columns.tolist()}")

# 5) Flag successful submissions via StatusWorkflow != 'Declined'
df['IsSuccessful'] = df['StatusWorkflow'] != 'Declined'

# 6) Helpers for reasons and banks
def collect_no_lead_reasons(grp):
    mask = grp['IsSuccessful'] & grp['LeadID'].isna()
    return grp.loc[mask].apply(lambda r: f"{r['LeadExclusionRuleId']} ({r['Status']})", axis=1).tolist()

def collect_unsuccessful_reasons(grp):
    return grp.loc[~grp['IsSuccessful'], 'StatusWorkflow'].dropna().unique().tolist()

def collect_successful_banks(grp):
    return grp.loc[grp['IsSuccessful'], 'ApplicantBank'].dropna().unique().tolist()

def collect_declined_banks(grp):
    return grp.loc[~grp['IsSuccessful'], 'ApplicantBank'].dropna().unique().tolist()

# 7) Aggregate per IdNumber
agg = df.groupby('IdNumber').agg(
    BusinessEntity          = ('BusinessEntity',     lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    RegisteredInNameOf      = ('RegisteredInNameOf', lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    SubmissionType          = ('SubmissionType',     lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    LeadIntakeDate          = ('LeadIntakeDate',     'min'),
    TotalSubmissions        = ('IdNumber',           'count'),
    SuccessfulSubmissions   = ('IsSuccessful',       'sum'),
    LeadCreated             = ('LeadID',             lambda x: 'Yes' if x.notna().any() else 'No'),
    HomeOwnersCoverCount    = ('LeadType',           lambda x: (x=='HomeOwnersCover').sum()),
    CreditLifeCount         = ('LeadType',           lambda x: (x=='CreditLife').sum()),
    BetterSureHomeCount     = ('LeadType',           lambda x: (x=='BetterSureHome').sum()),
)

# 8) Add reason-lists and bank-lists
agg['NoLeadReasonList']    = df.groupby('IdNumber').apply(collect_no_lead_reasons)
agg['UnsuccessfulReasons'] = df.groupby('IdNumber').apply(collect_unsuccessful_reasons)
agg['SuccessfulBanks']     = df.groupby('IdNumber').apply(collect_successful_banks)
agg['DeclinedBanks']       = df.groupby('IdNumber').apply(collect_declined_banks)

# 9) Build final summary and flag
summary_df = agg.reset_index()
summary_df['HadSuccessfulSubmission'] = summary_df['SuccessfulSubmissions'].apply(lambda x: 'Yes' if x > 0 else 'No')

# 10) Reorder columns
summary_df = summary_df[[
    'IdNumber',
    'BusinessEntity',
    'RegisteredInNameOf',
    'SubmissionType',
    'LeadIntakeDate',
    'TotalSubmissions',
    'SuccessfulSubmissions',
    'HadSuccessfulSubmission',
    'LeadCreated',
    'HomeOwnersCoverCount',
    'CreditLifeCount',
    'BetterSureHomeCount',
    'NoLeadReasonList',
    'UnsuccessfulReasons',
    'SuccessfulBanks',
    'DeclinedBanks'
]]

# 11) Export & display
summary_df.to_excel('intake_for_nb.xlsx', index=False)
print(summary_df)

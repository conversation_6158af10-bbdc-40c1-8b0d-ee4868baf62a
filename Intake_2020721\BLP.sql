WITH LatestLead AS (
    SELECT *
    FROM [BetterLifeReportingData_Prod].[dbo].[covermorelead3]
    where Lead_Product = 1
	and [Lead_LeadDate] > '2025-07-23 00:17:32.000'
)
--INSERT INTO [BetterLifeReportingData_Prod].[dbo].[ManualLeads] 
--(
--      [Lead_CampaignList]
--    , [DateFetched]
--    , [Lead_LeadDate]
--    , [Lead_ReadyForProcessingDate]
--    , [Lead_Product]
--    , [Details_ApplicantType]
--    , [Details_IDNumber]
--    , [Details_Title]
--    , [Details_Initials]
--    , [Details_FirstName]
--    , [Details_Surname]
--    , [Details_DateOfBirth]
--    , [Details_CellPhoneNumber]
--    , [Details_WorkPhoneNumber]
--    , [Details_HomePhoneNumber]
--    , [Details_EmailAddress]
--    , [Bond_ApplicationReference]
--    , [Bond_PurchasePrice]
--    , [Bond_LoanAmount]
--    , [Bond_Bank]
--    , [Bond_BondAttorney]
--    , [Entity_Region]
--    , [Consultant_FirstName]
--    , [Consultant_ConsultantId]
--    , [Property_PropertyDescription]
--    , [Property_AddressLine1]
--    , [Property_AddressLine2]
--    , [Property_City]
--    , [Property_Suburb]
--    , [Property_Province]
--    , [Property_PostalCode]
--    , [Property_IsNewDevelopment]
--    , [Bond_LoanType]
--    , [Property_PropertyType]
--    , [Entity_BusinessEntity]
--    , [Details_Gender]
--    , [Details_HomeLanguage]
--    , [Details_MaritalStatus]
--    , [Details_EthnicGroup]
--    , [Details_HighestLevelOfEducation]
--    , [Details_EmploymentStatus]
--    , [Details_GrossIncome]
--    , [Lead_ByPassExclusionRules] 
--    , [Processed]
--)
SELECT 
      '4377' AS [Lead_CampaignList]
    , GETDATE() AS [DateFetched]
    , [Lead_LeadDate]
    , [Lead_ReadyForProcessingDate]
    , [Lead_Product]
    , [Details_ApplicantType]
    , [Details_IDNumber]
    , [Details_Title]
    , [Details_Initials]
    , [Details_FirstName]
    , [Details_Surname]
    , [Details_DateOfBirth]
    , [Details_CellPhoneNumber]
    , [Details_WorkPhoneNumber]
    , [Details_HomePhoneNumber]
    , [Details_EmailAddress]
    , [Bond_ApplicationReference]
    , [Bond_PurchasePrice]
    , [Bond_LoanAmount]
    , [Bond_Bank]
    , [Bond_BondAttorney]
    , [Entity_Region]
    , [Consultant_FirstName]
    , [Consultant_ConsultantId]
    , [Property_PropertyDescription]
    , [Property_AddressLine1]
    , [Property_AddressLine2]
    , [Property_City]
    , [Property_Suburb]
    , [Property_Province]
    , [Property_PostalCode]
    , [Property_IsNewDevelopment]
    , [Bond_LoanType]
    , [Property_PropertyType]
    , [Entity_BusinessEntity]
    , [Details_Gender]
    , [Details_HomeLanguage]
    , [Details_MaritalStatus]
    , [Details_EthnicGroup]
    , [Details_HighestLevelOfEducation]
    , [Details_EmploymentStatus]
    , [Details_GrossIncome]
    , 1 AS [Lead_ByPassExclusionRules] 
    , 0 AS [Processed]
FROM LatestLead;

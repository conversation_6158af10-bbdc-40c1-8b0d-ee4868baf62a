# Product Requirements Document (PRD)
## Automated Latest File Detection and Processing System

### Document Information
- **Version**: 1.0
- **Date**: July 28, 2025
- **Author**: Data Analytics Team
- **Status**: Draft

---

## 1. Executive Summary

### 1.1 Problem Statement
Currently, the intake data processing workflow requires manual identification and hardcoding of file paths in Python scripts. The system processes intake files with date-based naming conventions (e.g., `intake_20250721v2.xlsx`) but lacks automated detection of the most recent file, leading to:
- Manual script updates for each new data batch
- Risk of processing outdated files
- Inefficient workflow requiring developer intervention
- Potential for human error in file selection

### 1.2 Solution Overview
Develop an automated file detection system that dynamically identifies and processes the latest intake file based on configurable criteria, eliminating manual path updates and ensuring consistent processing of the most current data.

---

## 2. Current State Analysis

### 2.1 Existing File Structure
```
Intake/
├── Intake_2020721/
│   ├── intake_20250721v2.xlsx (source file)
│   ├── intake_for_nb.xlsx (output file)
│   └── [other related files]
├── intake2.py (processing script)
└── [various other intake files]
```

### 2.2 Current Processing Flow
1. Manual identification of latest intake file
2. Hardcoded file path in `intake2.py` (line 4)
3. Data processing and transformation
4. Output generation to `intake_for_nb.xlsx`

### 2.3 File Naming Patterns Identified
- **Primary Pattern**: `intake_YYYYMMDD[version].xlsx`
  - Examples: `intake_20250721v2.xlsx`, `Intake_20250723.xlsx`
- **Directory Pattern**: `Intake_YYYYMMDD/`
- **Output Pattern**: `intake_for_nb.xlsx`

---

## 3. Requirements

### 3.1 Functional Requirements

#### 3.1.1 Core File Detection (Priority: High)
- **FR-001**: System shall automatically detect the latest intake file based on date in filename
- **FR-002**: System shall support multiple file naming conventions:
  - `intake_YYYYMMDD[version].xlsx`
  - `Intake_YYYYMMDD.xlsx`
  - Case-insensitive matching
- **FR-003**: System shall handle version suffixes (v1, v2, etc.) and select the highest version for a given date
- **FR-004**: System shall search in both root directory and subdirectories

#### 3.1.2 File Validation (Priority: High)
- **FR-005**: System shall validate file accessibility before processing
- **FR-006**: System shall verify file format compatibility (.xlsx)
- **FR-007**: System shall check minimum file size threshold (>1KB)
- **FR-008**: System shall validate required columns exist in the detected file

#### 3.1.3 Configuration Management (Priority: Medium)
- **FR-009**: System shall support configurable search directories
- **FR-010**: System shall allow customizable file naming patterns via regex
- **FR-011**: System shall support exclusion patterns for files to ignore
- **FR-012**: System shall maintain configuration in external file (JSON/YAML)

#### 3.1.4 Processing Integration (Priority: High)
- **FR-013**: System shall seamlessly integrate with existing `intake2.py` processing logic
- **FR-014**: System shall maintain backward compatibility with manual file specification
- **FR-015**: System shall preserve all current data transformation functionality

#### 3.1.5 Logging and Monitoring (Priority: Medium)
- **FR-016**: System shall log file detection decisions with timestamps
- **FR-017**: System shall provide detailed error messages for troubleshooting
- **FR-018**: System shall track processing history and file metadata

### 3.2 Non-Functional Requirements

#### 3.2.1 Performance
- **NFR-001**: File detection shall complete within 5 seconds for directories with <1000 files
- **NFR-002**: System shall handle directories with up to 10,000 files efficiently

#### 3.2.2 Reliability
- **NFR-003**: System shall have 99.9% uptime for file detection operations
- **NFR-004**: System shall gracefully handle file system errors and permission issues

#### 3.2.3 Maintainability
- **NFR-005**: Code shall follow PEP 8 Python style guidelines
- **NFR-006**: System shall include comprehensive unit tests (>90% coverage)
- **NFR-007**: Configuration changes shall not require code modifications

#### 3.2.4 Security
- **NFR-008**: System shall respect file system permissions
- **NFR-009**: System shall not expose sensitive file paths in logs

---

## 4. Technical Specification

### 4.1 Architecture Overview
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Config File   │    │  File Detector   │    │  Data Processor │
│   (JSON/YAML)   │───▶│     Module       │───▶│   (intake2.py)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Logger Module  │
                       └──────────────────┘
```

### 4.2 Core Components

#### 4.2.1 FileDetector Class
```python
class FileDetector:
    def __init__(self, config_path: str = "file_detector_config.json")
    def find_latest_file(self, search_dirs: List[str] = None) -> str
    def validate_file(self, file_path: str) -> bool
    def get_file_metadata(self, file_path: str) -> Dict
```

#### 4.2.2 Configuration Schema
```json
{
    "search_directories": [".", "./Intake_*"],
    "file_patterns": [
        "intake_\\d{8}v?\\d*\\.xlsx",
        "Intake_\\d{8}\\.xlsx"
    ],
    "exclusion_patterns": ["*_backup*", "*_temp*"],
    "validation": {
        "min_file_size_kb": 1,
        "required_extensions": [".xlsx"],
        "required_columns": ["MainApplicantIDNumber", "StatusWorkflow"]
    },
    "logging": {
        "level": "INFO",
        "file": "file_detector.log"
    }
}
```

### 4.3 Integration Points

#### 4.3.1 Modified intake2.py Structure
```python
# Replace hardcoded path with dynamic detection
from file_detector import FileDetector

detector = FileDetector()
file_path = detector.find_latest_file()
# Rest of existing processing logic remains unchanged
```

---

## 5. Implementation Plan

### 5.1 Phase 1: Core Development (Week 1-2)
- Develop FileDetector class with basic functionality
- Implement file pattern matching and date extraction
- Create configuration management system
- Basic unit tests

### 5.2 Phase 2: Integration & Validation (Week 3)
- Integrate with existing intake2.py script
- Implement file validation logic
- Add comprehensive error handling
- Extended test coverage

### 5.3 Phase 3: Enhancement & Monitoring (Week 4)
- Add logging and monitoring capabilities
- Performance optimization
- Documentation and user guide
- Production deployment

---

## 6. Success Criteria

### 6.1 Acceptance Criteria
- [ ] System automatically detects latest file without manual intervention
- [ ] Processing completes successfully with dynamically detected files
- [ ] All existing functionality preserved
- [ ] Configuration changes work without code modifications
- [ ] Comprehensive test coverage achieved
- [ ] Performance benchmarks met

### 6.2 Key Performance Indicators (KPIs)
- **Automation Rate**: 100% of file detection automated
- **Error Reduction**: 95% reduction in file selection errors
- **Time Savings**: 80% reduction in manual setup time
- **System Reliability**: 99.9% successful file detection rate

---

## 7. Risk Assessment

### 7.1 Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| File naming inconsistencies | High | Medium | Flexible regex patterns, manual override |
| Performance with large directories | Medium | Low | Optimized search algorithms, caching |
| Integration breaking existing workflow | High | Low | Comprehensive testing, backward compatibility |

### 7.2 Business Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Processing wrong file | High | Low | Multi-layer validation, confirmation prompts |
| System downtime during transition | Medium | Medium | Phased rollout, rollback plan |

---

## 8. Future Enhancements

### 8.1 Potential Extensions
- **Real-time File Monitoring**: Watch directories for new files
- **Multi-format Support**: Handle CSV, XLSB, and other formats
- **Cloud Integration**: Support for cloud storage (OneDrive, SharePoint)
- **Notification System**: Alert users when new files are processed
- **Data Quality Checks**: Automated validation of file contents
- **Scheduling Integration**: Automatic processing on file arrival

### 8.2 Scalability Considerations
- Support for multiple concurrent processing pipelines
- Distributed file detection across network drives
- Integration with enterprise workflow systems

---

## 9. Appendices

### 9.1 Current File Analysis
Based on the existing structure, the following files were identified:
- `intake_20250721v2.xlsx` (current source)
- `Intake_20250723.xlsx` (newer potential source)
- Various CSV exports and processed files

### 9.2 Dependencies
- Python 3.7+
- pandas library
- openpyxl for Excel file handling
- pathlib for file system operations
- logging module for monitoring

---

**Document Status**: Ready for Review and Implementation
**Next Steps**: Technical review, resource allocation, and development kickoff

﻿-- 1) Local parameters
DECLARE 
    @StartDate     DATETIME      = '2025-01-01T00:00:00', 
    @EndDate       DATETIME      = '2025-07-20T23:59:59',
    @BadConsultant NVARCHAR(100) = 'Ross, Mariska',
    @BadAgency     NVARCHAR(100) = 'Evo Property';

-- 2) Early‐filter local submissions (all columns)
;WITH FilteredSubs AS (
    SELECT
        s.* 
    FROM DW_STG_MO.dbo.Submissions AS s
    WITH (READCOMMITTED)
    WHERE s.IntakeDate BETWEEN @StartDate AND @EndDate
      AND s.Consultant <> @BadConsultant
      AND s.Agency     <> @BadAgency
)

-- 3) LEFT JOIN into an OPENQUERY that filters on Status + Product
SELECT
    s.*,
    l.Status,
    l.ProcessedDate,
    l.LeadId,
    l.Product,
    l.LeadGenerationRuleId,
    l.LeadGenerationRuleDescription,
    l.LeadExclusionRuleId,
    l.LeadExclusionRuleDescription,
	l.ProcessingComment
FROM FilteredSubs AS s
LEFT JOIN OPENQUERY(
    [HO-VSSQL-SVR01],   -- your linked‐server alias
    N'
      SELECT 
        TriggerRecordId,
        Status,
        ProcessedDate,
        LeadId,
        Product,
        LeadGenerationRuleId,
        LeadGenerationRuleDescription,
        LeadExclusionRuleId,
        LeadExclusionRuleDescription,
		ProcessingComment
      FROM BetterLifeReportingData_Prod.dbo.Lead_Lead
      WHERE 
         Product IN (''BetterSureHome'', ''CreditLife'', ''HomeOwnersCover'')
    '
) AS l
  ON l.TriggerRecordId = s.SubRef
ORDER BY s.MainApplicantIDNumber;

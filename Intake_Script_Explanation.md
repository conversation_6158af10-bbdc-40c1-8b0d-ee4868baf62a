# Intake Data Processing Script Explanation
## How intake2.py Compiles the intake_for_nb.xlsx File

### Document Information
- **Script**: intake2.py
- **Input**: intake_20250721v2.xlsx
- **Output**: intake_for_nb.xlsx
- **Purpose**: Transform raw submission data into person-centric lead analysis

---

## Overview

The `intake2.py` script processes raw insurance submission data and transforms it from a **submission-centric** format (multiple rows per person) into a **person-centric** format (one summary row per unique ID number). This enables lead analysis and campaign planning.

---

## Step-by-Step Data Transformation Process

### **Step 1: Data Loading and Column Normalization**
```python
# Load the raw Excel file
file_path = r"...\intake_20250721v2.xlsx"
df = pd.read_excel(file_path)

# Normalize column names (lowercase + remove spaces/underscores)
df.columns = df.columns.str.lower().str.replace(r'[\s_]+', '', regex=True)
```
**Purpose**: Standardize column names for consistent processing

### **Step 2: Column Mapping to Standard Fields**
```python
col_map = {
    'mainapplicantidnumber': 'IdNumber',
    'businessentity': 'BusinessEntity',
    'registeredinthenameof': 'RegisteredInNameOf',
    'submissiontype': 'SubmissionType',
    'statusworkflow': 'StatusWorkflow',
    'leadid': 'LeadID',
    'product': 'LeadType',
    'processingcomment': 'LeadProcessComment',
    'leadexclusionruleid': 'LeadExclusionRuleId',
    'status': 'Status',
    'intakedate': 'LeadIntakeDate',
    'bankname': 'ApplicantBank'
}
```
**Purpose**: Map raw column names to standardized business field names

### **Step 3: Success Flag Creation**
```python
df['IsSuccessful'] = df['StatusWorkflow'] != 'Declined'
```
**Business Logic**: A submission is successful if it wasn't declined by the workflow

### **Step 4: Helper Functions for Data Collection**

#### **No Lead Reasons Collection**
```python
def collect_no_lead_reasons(grp):
    mask = grp['IsSuccessful'] & grp['LeadID'].isna()
    return grp.loc[mask].apply(lambda r: f"{r['LeadExclusionRuleId']} ({r['Status']})", axis=1).tolist()
```
**Purpose**: Capture why leads weren't created for successful submissions

#### **Bank Information Collection**
```python
def collect_successful_banks(grp):
    return grp.loc[grp['IsSuccessful'], 'ApplicantBank'].dropna().unique().tolist()

def collect_declined_banks(grp):
    return grp.loc[~grp['IsSuccessful'], 'ApplicantBank'].dropna().unique().tolist()
```
**Purpose**: Track which banks accepted vs declined each person

### **Step 5: Data Aggregation by ID Number**
```python
agg = df.groupby('IdNumber').agg(
    BusinessEntity          = ('BusinessEntity', lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    RegisteredInNameOf      = ('RegisteredInNameOf', lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    SubmissionType          = ('SubmissionType', lambda x: x.dropna().iloc[0] if x.dropna().any() else None),
    LeadIntakeDate          = ('LeadIntakeDate', 'min'),
    TotalSubmissions        = ('IdNumber', 'count'),
    SuccessfulSubmissions   = ('IsSuccessful', 'sum'),
    LeadCreated             = ('LeadID', lambda x: 'Yes' if x.notna().any() else 'No'),
    HomeOwnersCoverCount    = ('LeadType', lambda x: (x=='HomeOwnersCover').sum()),
    CreditLifeCount         = ('LeadType', lambda x: (x=='CreditLife').sum()),
    BetterSureHomeCount     = ('LeadType', lambda x: (x=='BetterSureHome').sum()),
)
```

**Key Aggregation Logic**:
- **Personal Info**: Takes first non-null value (same person, multiple submissions)
- **Date**: Uses earliest intake date (`min`)
- **Counts**: Sums up submissions, successes, and product types
- **Lead Status**: 'Yes' if any submission generated a LeadID, 'No' otherwise

### **Step 6: Reason and Bank List Addition**
```python
agg['NoLeadReasonList'] = df.groupby('IdNumber').apply(collect_no_lead_reasons)
agg['UnsuccessfulReasons'] = df.groupby('IdNumber').apply(collect_unsuccessful_reasons)
agg['SuccessfulBanks'] = df.groupby('IdNumber').apply(collect_successful_banks)
agg['DeclinedBanks'] = df.groupby('IdNumber').apply(collect_declined_banks)
```
**Purpose**: Add detailed reason lists and bank information for each person

### **Step 7: Final Summary Creation**
```python
summary_df = agg.reset_index()
summary_df['HadSuccessfulSubmission'] = summary_df['SuccessfulSubmissions'].apply(lambda x: 'Yes' if x > 0 else 'No')
```

### **Step 8: Column Reordering and Export**
```python
summary_df = summary_df[[
    'IdNumber', 'BusinessEntity', 'RegisteredInNameOf', 'SubmissionType',
    'LeadIntakeDate', 'TotalSubmissions', 'SuccessfulSubmissions',
    'HadSuccessfulSubmission', 'LeadCreated', 'HomeOwnersCoverCount',
    'CreditLifeCount', 'BetterSureHomeCount', 'NoLeadReasonList',
    'UnsuccessfulReasons', 'SuccessfulBanks', 'DeclinedBanks'
]]

summary_df.to_excel('intake_for_nb.xlsx', index=False)
```

---

## Data Transformation Example

### **Input (Raw Submissions)**
| IdNumber | StatusWorkflow | LeadID | Product | ApplicantBank |
|----------|----------------|--------|---------|---------------|
| 123456   | Granted        | L001   | HOC     | FNB          |
| 123456   | Declined       | NULL   | CreditLife | ABSA      |
| 123456   | Granted        | NULL   | BetterSure | Standard   |

### **Output (Person Summary)**
| IdNumber | TotalSubmissions | SuccessfulSubmissions | LeadCreated | SuccessfulBanks | DeclinedBanks |
|----------|------------------|----------------------|-------------|-----------------|---------------|
| 123456   | 3                | 2                    | Yes         | [FNB, Standard] | [ABSA]        |

---

## Key Business Insights Generated

1. **Lead Creation Status**: Who got leads vs who didn't
2. **Bank Performance**: Which banks accept/decline each person
3. **Product Interest**: What insurance types each person applied for
4. **Failure Reasons**: Why leads weren't created for successful submissions
5. **Submission Patterns**: How many times each person applied

---

## Output File Usage

The `intake_for_nb.xlsx` file serves as the foundation for:
- **Lead Analysis**: Identifying conversion opportunities
- **Campaign Planning**: Targeting people without leads
- **Bank Relationship Management**: Understanding bank approval patterns
- **Product Strategy**: Analyzing product demand and success rates

This processed data enables the next phase: **Campaign Creation for Intake Leads**

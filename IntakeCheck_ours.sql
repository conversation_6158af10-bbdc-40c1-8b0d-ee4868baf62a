﻿-- 1) Local parameters
DECLARE 
    @StartDate     DATETIME      = '2025-01-01T00:00:00', 
    @EndDate       DATETIME      = '2025-07-20T23:59:59',
    @BadConsultant NVARCHAR(100) = 'Ross, Mariska',
    @BadAgency     NVARCHAR(100) = 'Evo Property';

-- 2) Early‐filter local submissions
;WITH FilteredSubs AS (
    SELECT
        SubId, AppId, SubRef, BankId,BusinessEntityID,
	BusinessEntity,
        Consultant, Agency, IntakeDate,
        MainApplicantIDNumber
    FROM DW_STG_MO.dbo.Submissions WITH (READCOMMITTED)
    WHERE IntakeDate BETWEEN @StartDate AND @EndDate
      AND Consultant <> @BadConsultant
      AND Agency     <> @BadAgency
)

-- 3) OPENQUERY with proper quoting
SELECT
    s.SubId,
    s.AppId,
    s.SubRef,
    s.BankId,
	s.BusinessEntityID,
	s.BusinessEntity,
    l.Status,
    l.ProcessedDate,
    l.Lead<PERSON>,
    l.Product,
    l.LeadGenerationRuleId,
    l.LeadGenerationRuleDescription,
    l.<PERSON>uleId,
    l.LeadExclusionRuleDescription,
    s.<PERSON>ApplicantIDNumber
FROM FilteredSubs AS s
INNER JOIN OPENQUERY(
    [HO-VSSQL-SVR01],  -- your linked-server alias
    N'
      SELECT
        TriggerRecordId,
        Status,
        ProcessedDate,
        LeadId,
        Product,
        LeadGenerationRuleId,
        LeadGenerationRuleDescription,
        LeadExclusionRuleId,
        LeadExclusionRuleDescription
      FROM BetterLifeReportingData_Prod.dbo.Lead_Lead
      WHERE Status = ''Success''
	  
    '
) AS l
  ON l.TriggerRecordId = s.SubRef
ORDER BY s.MainApplicantIDNumber;

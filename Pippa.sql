﻿-- 1) Local parameters
DECLARE 
    @StartDate     DATETIME      = '2025-01-01T00:00:00', 
    @EndDate       DATETIME      = '2025-06-30T23:59:59',
    @BadConsultant NVARCHAR(100) = 'Ross, Mariska',
    @BadAgency     NVARCHAR(100) = 'Evo Property';

-- 2) Early‐filter local submissions
;WITH FilteredSubs AS (
    SELECT
        SubId,
        AppId,
        SubRef,
        BankId,
        Consultant,
        Agency,
        IntakeDate,
        MainApplicantIDNumber,
		StatusDescription
    FROM DW_STG_MO.dbo.Submissions WITH (READCOMMITTED)
    WHERE IntakeDate BETWEEN @StartDate AND @EndDate
      AND Consultant <> @BadConsultant
      AND Agency     <> @BadAgency
)

-- 3) Join to Applicants and remote Lead data
SELECT
    s.SubId,
    s.AppId,
    s.SubRef,
    s.BankId,
	s.StatusDescription,
    s.Consultant,               -- added consultant
    a.<PERSON>,
    a.<PERSON>,
    a.<PERSON>,
    a.<PERSON>,
    a.Surname,
    a.IDNumber     AS ApplicantIDNumber,
    a.<PERSON>,
    l.<PERSON>,
    l.ProcessedDate,
    l.Lead<PERSON>d,
    l.Product,
    l.LeadGenerationRuleId,
    l.LeadGenerationRuleDescription,
    l.LeadExclusionRuleId,
    l.LeadExclusionRuleDescription,
    s.MainApplicantIDNumber
FROM FilteredSubs AS s
-- bring in applicant demographic/contact info
INNER JOIN DW_STG_MO.dbo.Applicants AS a
    ON a.SubId = s.SubId
-- bring in only successful leads from the remote system
INNER JOIN OPENQUERY(
    [HO-VSSQL-SVR01],
    N'
      SELECT
        TriggerRecordId,
        Status,
        ProcessedDate,
        LeadId,
        Product,
        LeadGenerationRuleId,
        LeadGenerationRuleDescription,
        LeadExclusionRuleId,
        LeadExclusionRuleDescription
      FROM BetterLifeReportingData_Prod.dbo.Lead_Lead
      WHERE Status = ''Success''
    '
) AS l
    ON l.TriggerRecordId = s.SubRef
ORDER BY s.MainApplicantIDNumber;


--1
----------------------
USE BetterLifeReportingData_Prod;
GO


--2
-----------------

CREATE TABLE #TriggerPatterns (
    Pattern NVARCHAR(500) NOT NULL
);
GO


--3
-------------------------
INSERT INTO #TriggerPatterns (Pattern)
VALUES
    (N'DMSKEM20250115002'),
(N'DMSJNR20250115028'),
(N'DMSJNR20250115033'),
(N'DMSJNR20250115031'),
(N'DMSCPT20250115034'),
(N'DMSBFN20250115016'),
(N'DMSCPT20250114027'),
(N'DMSJNR20250115032'),
(N'DMSBOK20250115016'),
(N'DMSBFN20250115014'),
(N'DMSCPT20250115003'),
(N'DMSJNR20250115025'),
(N'DMSCPT20250115032'),
(N'DMSJNR20250116003'),
(N'DMSBFN20250115017'),
(N'DMSBOK20241220013'),
(N'DMSDBN20250116004'),
(N'DMSCPT20250116002'),
(N'DMSBFN20250115012'),
(N'DMSBFN20250116004'),
(N'DMSPTA20250115027'),
(N'DMSCPT20250115004'),
(N'DMSBFN20250116001'),
(N'DMSCPT20250116005'),
(N'DMSPEB20250115005'),
(N'DMSJNR20250115027'),
(N'DMSBOK20250115002'),
(N'DMSBOK20250114021'),
(N'DMSJNR20250115024'),
(N'DMSCPT20240917042'),
(N'DMSDBN20250115010'),
(N'DMSPTA20250116001'),
(N'DMSJNR20250116005'),
(N'DMSBOK20250116005'),
(N'DMSDBN20250115020'),
(N'DMSDBN20250116005'),
(N'DMSPEB20250113003'),
(N'DMSBOK20250116007'),
(N'DMSDBN20250108012'),
(N'DMSBFN20250115018'),
(N'DMSJNR20250116001'),
(N'DMSBOK20250116004'),
(N'DMSBOK20250114018'),
(N'DMSJNR20250116008'),
(N'DMSBFN20250116008'),
(N'DMSCPT20250116018'),
(N'DMSBOK20250116002'),
(N'DMSCPT20250115023'),
(N'DMSDBN20250113011'),
(N'DMSPTA20250116009'),
(N'DMSPTA20250116006'),
(N'DMSCPT20250115028'),
(N'DMSBFN20250116005'),
(N'DMSJNR20250116010'),
(N'DMSBOK20250115008'),
(N'DMSDBN20250113018'),
(N'DMSJNR20250116007'),
(N'DMSBOK20250116011'),
(N'DMSJNR20250116012'),
(N'DMSBFN20250116010'),
(N'DMSDBN20250116009'),
(N'DMSBOK20250206011'),
(N'DMSPTA20250206009'),
(N'DMSJNR20250206003'),
(N'DMSPTA20250205025'),
(N'DMSDBN20250206010'),
(N'DMSEDE20250204001'),
(N'DMSBOK20250206015'),
(N'DMSJNR20250206018'),
(N'DMSJNR20250206012'),
(N'DMSPTA20250204030'),
(N'DMSBFN20250206005'),
(N'DMSDBN20250206011'),
(N'DMSPTA20250206010'),
(N'DMSBOK20250203022'),
(N'DMSPTA20250205026'),
(N'DMSPTA20250205024'),
(N'DMSPTA20250206011'),
(N'DMSMUL20250206002'),
(N'DMSBFN20250206004'),
(N'DMSCPT20250204018'),
(N'DMSGAR20250205001'),
(N'DMSDBN20250206006'),
(N'DMSCPT20250206012'),
(N'DMSPEB20250206006'),
(N'DMSPTA20250206014'),
(N'DMSJNR20250206016'),
(N'DMSBOK20250206019'),
(N'DMSJNR20250206011'),
(N'DMSPTA20250206016'),
(N'DMSCPT20250203029'),
(N'DMSBOK20250206012'),
(N'DMSDBN20250206007'),
(N'DMSPEB20250206004'),
(N'DMSCPT20250206011'),
(N'DMSBOK20250206016'),
(N'DMSPTA20250206015'),
(N'DMSBOK20250205020'),
(N'DMSBOK20250131004'),
(N'DMSBFN20250206006'),
(N'DMSPEB20250206007'),
(N'DMSBFN20250203001'),
(N'DMSBFN20250205001'),
(N'DMSBOK20250206014'),
(N'DMSCPT20250205030'),
(N'DMSPTA20250204011'),
(N'DMSDBN20250206016'),
(N'DMSKEM20250206004'),
(N'DMSPTA20250204002'),
(N'DMSEDE20250206001'),
(N'DMSBFN20250205016'),
(N'DMSBFN20250206009'),
(N'DMSJNR20250206017'),
(N'DMSDBN20250205011'),
(N'DMSDBN20250206013'),
(N'DMSJNR20250206021'),
(N'DMSPTA20250206018'),
(N'DMSPTA20250206019'),
(N'DMSBOK20250206017'),
(N'DMSBOK20250206018'),
(N'DMSCPT20250206022'),
(N'DMSJNR20250206019'),
(N'DMSPEB20250206008'),
(N'DMSPTA20250206017'),
(N'DMSBOK20250206025'),
(N'DMSPTA20250206005'),
(N'DMSDBN20250206014'),
(N'DMSBOK20250206003'),
(N'DMSCPT20250206010'),
(N'DMSDBN20250206008'),
(N'DMSCPT20250206008'),
(N'DMSJNR20250206022'),
(N'DMSDBN20250206018'),
(N'DMSBFN20250206008'),
(N'DMSMUL20250206003'),
(N'DMSDBN20250204014'),
(N'DMSCPT20250129001'),
(N'DMSBOK20250206022'),
(N'DMSBFN20250206011'),
(N'DMSBOK20250206021'),
(N'DMSBOK20250206026'),
(N'DMSDBN20250227017'),
(N'DMSBFN20250227011'),
(N'DMSCPT20250227029'),
(N'DMSBFN20250227017'),
(N'DMSDBN20250224021'),
(N'DMSCPT20250227039'),
(N'DMSDBN20250225016'),
(N'DMSPTA20250226017'),
(N'DMSJNR20250227003'),
(N'DMSJNR20250227011'),
(N'DMSBOK20250227019'),
(N'DMSCPT20250227013'),
(N'DMSJNR20250227014'),
(N'DMSJNR20250226028'),
(N'DMSBFN20250227018'),
(N'DMSBOK20250227020'),
(N'DMSCPT20250227034'),
(N'DMSPTA20250227018'),
(N'DMSBFN20250227020'),
(N'DMSCPT20250227037'),
(N'DMSCPT20250227038'),
(N'DMSBOK20250228002'),
(N'DMSDBN20250228002'),
(N'DMSBOK20250227016'),
(N'DMSPTA20250228001'),
(N'DMSDBN20250226016'),
(N'DMSKEM20250227002'),
(N'DMSBOK20250227013'),
(N'DMSMUL20250228001'),
(N'DMSDBN20250217016'),
(N'DMSPEB20250227004'),
(N'DMSDBN20250228003'),
(N'DMSDBN20250221013'),
(N'DMSJNR20250227022'),
(N'DMSPTA20250228002'),
(N'DMSPTA20250226011'),
(N'DMSBOK20250225012'),
(N'DMSDBN20250228004'),
(N'DMSJNR20250226015'),
(N'DMSDBN20250228006'),
(N'DMSMUL20250228002'),
(N'DMSPTA20250228003'),
(N'DMSBOK20250225006'),
(N'DMSCPT20250227020'),
(N'DMSJNR20250227023'),
(N'DMSPEB20250228001'),
(N'DMSBOK20250225031'),
(N'DMSBFN20250227016'),
(N'DMSDBN20250225008'),
(N'DMSDBN20250227014'),
(N'DMSPTA20250228005'),
(N'DMSJNR20250227019'),
(N'DMSPEB20250226001'),
(N'DMSPEB20250220003'),
(N'DMSJNR20250228003'),
(N'DMSCPT20250225020'),
(N'DMSBOK20250228007'),
(N'DMSKEM20250228002'),
(N'DMSCPT20250324003'),
(N'DMSPTA20250324017'),
(N'DMSGAR20250324001'),
(N'DMSPTA20250320006'),
(N'DMSJNR20250324019'),
(N'DMSBFN20250324004'),
(N'DMSPEB20250319006'),
(N'DMSDBN20250324001'),
(N'DMSBOK20250324030'),
(N'DMSBOK20250324031'),
(N'DMSCPT20250318049'),
(N'DMSPEB20250324009'),
(N'DMSPTA20250324019'),
(N'DMSBOK20250324029'),
(N'DMSCPT20250324035'),
(N'DMSCPT20250324038'),
(N'DMSBFN20250324013'),
(N'DMSPEB20250324002'),
(N'DMSCPT20250324045'),
(N'DMSKEM20250312005'),
(N'DMSPEB20250318003'),
(N'DMSPTA20250324021'),
(N'DMSPTA20250324022'),
(N'DMSJNR20250324007'),
(N'DMSBFN20250324015'),
(N'DMSJNR20250324032'),
(N'DMSBFN20250324016'),
(N'DMSCPT20250324024'),
(N'DMSPTA20250323002'),
(N'DMSBFN20250325001'),
(N'DMSJNR20250324023'),
(N'DMSJNR20250324010'),
(N'DMSCPT20250324025'),
(N'DMSBOK20250320019'),
(N'DMSBFN20250325002'),
(N'DMSCPT20250325001'),
(N'DMSCPT20250325002'),
(N'DMSMUL20250324003'),
(N'DMSBOK20250324007'),
(N'DMSCPT20250303046'),
(N'DMSBOK20250324023'),
(N'DMSPTA20250324023'),
(N'DMSCPT20250324028'),
(N'DMSDBN20250324016'),
(N'DMSBOK20250325003'),
(N'DMSJNR20250324014'),
(N'DMSCPT20250318048'),
(N'DMSCPT20250205029'),
(N'DMSJNR20250325004'),
(N'DMSPTA20250325009'),
(N'DMSJNR20250324027'),
(N'DMSBOK20250324012'),
(N'DMSDBN20250324005'),
(N'DMSPTA20250325003'),
(N'DMSDBN20250114003'),
(N'DMSJNR20250114010'),
(N'DMSBOK20250114011'),
(N'DMSCPT20250113020'),
(N'DMSDBN20250113015'),
(N'DMSCPT20250110023'),
(N'DMSDBN20250114007'),
(N'DMSDBN20250114004'),
(N'DMSBOK20250113014'),
(N'DMSCPT20250114018'),
(N'DMSCPT20250114009'),
(N'DMSCPT20250114004'),
(N'DMSKEM20250113004'),
(N'DMSBOK20250113008'),
(N'DMSJNR20250110005'),
(N'DMSBOK20250114007'),
(N'DMSPEB20241231001'),
(N'DMSCPT20250114015'),
(N'DMSCPT20250114006'),
(N'DMSCPT20250113013'),
(N'DMSJNR20250113026'),
(N'DMSPTA20250113021'),
(N'DMSDBN20250113004'),
(N'DMSBOK20250113009'),
(N'DMSBOK20250114013'),
(N'DMSPEB20250114002'),
(N'DMSCPT20250114017'),
(N'DMSCPT20250114012'),
(N'DMSKEM20250113002'),
(N'DMSBOK20250114016'),
(N'DMSBFN20250114004'),
(N'DMSBOK20250113023'),
(N'DMSCPT20241212009'),
(N'DMSCPT20241212007'),
(N'DMSJNR20250114011'),
(N'DMSCPT20250113015'),
(N'DMSPEB20250114003'),
(N'DMSPTA20250109001'),
(N'DMSDBN20250114012'),
(N'DMSCPT20250106007'),
(N'DMSJNR20250113029'),
(N'DMSBOK20250114019'),
(N'DMSJNR20250114008'),
(N'DMSPTA20250114003'),
(N'DMSCPT20250114021'),
(N'DMSBOK20250114017'),
(N'DMSCPT20250113023'),
(N'DMSCPT20250114003'),
(N'DMSBOK20250114020'),
(N'DMSBFN20250114007'),
(N'DMSJNR20250114012'),
(N'DMSJNR20250114013'),
(N'DMSJNR20250114007'),
(N'DMSDBN20250112002'),
(N'DMSJNR20250114006'),
(N'DMSBFN20250114010'),
(N'DMSBOK20250114010'),
(N'DMSBFN20250114008'),
(N'DMSPEB20250114004'),
(N'DMSCPT20250114024'),
(N'DMSDBN20250113014'),
(N'DMSGAR20250114001'),
(N'DMSCPT20250114025'),
(N'DMSJNR20250114014'),
(N'DMSBOK20250114023'),
(N'DMSBOK20250114026'),
(N'DMSPEB20250114006'),
(N'DMSCPT20250106016'),
(N'DMSDBN20250114014'),
(N'DMSDBN20250114009'),
(N'DMSJNR20250113021'),
(N'DMSJNR20250114020'),
(N'DMSBOK20250203002'),
(N'DMSKIM20250130001'),
(N'DMSBOK20250203001'),
(N'DMSCPT20250131011'),
(N'DMSPTA20250202002'),
(N'DMSCPT20250127037'),
(N'DMSPEB20250131005'),
(N'DMSDBN20250203005'),
(N'DMSCPT20250203008'),
(N'DMSPTA20250131003'),
(N'DMSPEB20250203001'),
(N'DMSBOK20250203011'),
(N'DMSJNR20250203006'),
(N'DMSPEB20250130001'),
(N'DMSDBN20250130017'),
(N'DMSBOK20250203015'),
(N'DMSBOK20250131006'),
(N'DMSJNR20250203012'),
(N'DMSBOK20250203016'),
(N'DMSBOK20250203014'),
(N'DMSPTA20250203008'),
(N'DMSJNR20250124011'),
(N'DMSCPT20250203010'),
(N'DMSJNR20250203008'),
(N'DMSDBN20250130019'),
(N'DMSJNR20250203007'),
(N'DMSDBN20250203002'),
(N'DMSJNR20250203009'),
(N'DMSBOK20250131020'),
(N'DMSPTA20250131010'),
(N'DMSDBN20250131005'),
(N'DMSDBN20250203010'),
(N'DMSPTA20250203006'),
(N'DMSJNR20250203019'),
(N'DMSCPT20250131022'),
(N'DMSJNR20250203015'),
(N'DMSPTA20250203015'),
(N'DMSDBN20250203009'),
(N'DMSPTA20250203013'),
(N'DMSCPT20250203002'),
(N'DMSPTA20250201002'),
(N'DMSCPT20250130027'),
(N'DMSDBN20250203013'),
(N'DMSPTA20250203014'),
(N'DMSJNR20250203023'),
(N'DMSJNR20250203025'),
(N'DMSPTA20250201001'),
(N'DMSJNR20250203024'),
(N'DMSJNR20250203018'),
(N'DMSJNR20250203001'),
(N'DMSJNR20250203016'),
(N'DMSBOK20250203018'),
(N'DMSBOK20250203013'),
(N'DMSBOK20250130010'),
(N'DMSPTA20250203004'),
(N'DMSPTA20250131023'),
(N'DMSPTA20250130014'),
(N'DMSCPT20250131038'),
(N'DMSDBN20250203014'),
(N'DMSBFN20250203004'),
(N'DMSJNR20250203020'),
(N'DMSCPT20250203016'),
(N'DMSPTA20250203001'),
(N'DMSDBN20250130012'),
(N'DMSPTA20250202007'),
(N'DMSCPT20250203015'),
(N'DMSPTA20250203019'),
(N'DMSCPT20250110006'),
(N'DMSPTA20250130009'),
(N'DMSJNR20250203026'),
(N'DMSJNR20250219028'),
(N'DMSJNR20250219026'),
(N'DMSPTA20250219025'),
(N'DMSCPT20250218038'),
(N'DMSCPT20250219033'),
(N'DMSCPT20250219008'),
(N'DMSPTA20250214015'),
(N'DMSCPT20250219044'),
(N'DMSBOK20250219025'),
(N'DMSJNR20250212021'),
(N'DMSPTA20250218021'),
(N'DMSDBN20250220005'),
(N'DMSBOK20250219033'),
(N'DMSJNR20250219022'),
(N'DMSDBN20250219019'),
(N'DMSJNR20250219016'),
(N'DMSEDE20250220001'),
(N'DMSDBN20250220006'),
(N'DMSBOK20250220006'),
(N'DMSJNR20250220001'),
(N'DMSPEB20250220002'),
(N'DMSJNR20250220003'),
(N'DMSPTA20250204017'),
(N'DMSJNR20250214004'),
(N'DMSBFN20250219005'),
(N'DMSKIM20250217002'),
(N'DMSPTA20250219024'),
(N'DMSDBN20250220007'),
(N'DMSBOK20250220005'),
(N'DMSBFN20250220002'),
(N'DMSPTA20250220001'),
(N'DMSCPT20250219041'),
(N'DMSDBN20250220012'),
(N'DMSPTA20250219016'),
(N'DMSCPT20250219043'),
(N'DMSBOK20250219027'),
(N'DMSCPT20250220008'),
(N'DMSJNR20250214014'),
(N'DMSJNR20250219027'),
(N'DMSCPT20250220005'),
(N'DMSJNR20250217007'),
(N'DMSJNR20250219020'),
(N'DMSDBN20250220008'),
(N'DMSBFN20250217007'),
(N'DMSJNR20250220002'),
(N'DMSCPT20250219040'),
(N'DMSBOK20250219004'),
(N'DMSPTA20250217026'),
(N'DMSJNR20250220004'),
(N'DMSCPT20250219045'),
(N'DMSCPT20250220003'),
(N'DMSCPT20250218012'),
(N'DMSKEM20250220001'),
(N'DMSBOK20250220009'),
(N'DMSDBN20250220002'),
(N'DMSBFN20250220004'),
(N'DMSDBN20250220014'),
(N'DMSJNR20250220012'),
(N'DMSDBN20250220011'),
(N'DMSJNR20250220011'),
(N'DMSBOK20250218020'),
(N'DMSJNR20250219018'),
(N'DMSPTA20250311007'),
(N'DMSCPT20250310026'),
(N'DMSCPT20250311036'),
(N'DMSJNR20250311015'),
(N'DMSCPT20250311038'),
(N'DMSBOK20250310023'),
(N'DMSJNR20250311011'),
(N'DMSPTA20250311009'),
(N'DMSBOK20250311015'),
(N'DMSBFN20250311010'),
(N'DMSCPT20250310011'),
(N'DMSPEB20250310007'),
(N'DMSCPT20250311033'),
(N'DMSPEB20250311006'),
(N'DMSBOK20250311016'),
(N'DMSCPT20250311053'),
(N'DMSPTA20250306004'),
(N'DMSDBN20250311012'),
(N'DMSDBN20250311014'),
(N'DMSBOK20250311006'),
(N'DMSJNR20250311012'),
(N'DMSCPT20250307021'),
(N'DMSBFN20250311007'),
(N'DMSJNR20250311010'),
(N'DMSCPT20250310019'),
(N'DMSDBN20250311013'),
(N'DMSPTA20250310009'),
(N'DMSBFN20250311011'),
(N'DMSJNR20250311008'),
(N'DMSCPT20250311047'),
(N'DMSDBN20250303026'),
(N'DMSCPT20250311043'),
(N'DMSCPT20250311042'),
(N'DMSCPT20250311050'),
(N'DMSMUL20250311001'),
(N'DMSCPT20250311044'),
(N'DMSBOK20250306002'),
(N'DMSBFN20250310004'),
(N'DMSPTA20250311016'),
(N'DMSCPT20250311032'),
(N'DMSBOK20250311019'),
(N'DMSDBN20250311015'),
(N'DMSCPT20250311052'),
(N'DMSBOK20250306020'),
(N'DMSBOK20250311017'),
(N'DMSBOK20250310011'),
(N'DMSCPT20250311045'),
(N'DMSBFN20250305008'),
(N'DMSCPT20250307012'),
(N'DMSJNR20250311017'),
(N'DMSPTA20250311003'),
(N'DMSPTA20250311012'),
(N'DMSCPT20250311024'),
(N'DMSBFN20250311008'),
(N'DMSCPT20250311010'),
(N'DMSJNR20250311018'),
(N'DMSBFN20250311006'),
(N'DMSBOK20250311024'),
(N'DMSJNR20250310016'),
(N'DMSCPT20250310006'),
(N'DMSJNR20250311001'),
(N'DMSJNR20250311013'),
(N'DMSBOK20250311022'),
(N'DMSBOK20250307001'),
(N'DMSJNR20250311016'),
(N'DMSCPT20250311054'),
(N'DMSDBN20250306008'),
(N'DMSCPT20250307023'),
(N'DMSBOK20250311005'),
(N'DMSBOK20250311025'),
(N'DMSJNR20250311014'),
(N'DMSPTA20250331002'),
(N'DMSPTA20250328008'),
(N'DMSBOK20250331017'),
(N'DMSJNR20250328001'),
(N'DMSDBN20250325015'),
(N'DMSBFN20250318010'),
(N'DMSBFN20250326007'),
(N'DMSJNR20250331026'),
(N'DMSDBN20250331013'),
(N'DMSBOK20250331013'),
(N'DMSBFN20250331016'),
(N'DMSBFN20250331015'),
(N'DMSCPT20250331024'),
(N'DMSBOK20250331011'),
(N'DMSJNR20250331019'),
(N'DMSBOK20250331038'),
(N'DMSBFN20250331008'),
(N'DMSJNR20250331029'),
(N'DMSCPT20250103007'),
(N'DMSCPT20250108016'),
(N'DMSKEM20250107003'),
(N'DMSCPT20250108010'),
(N'DMSBOK20250108001'),
(N'DMSCPT20250108015'),
(N'DMSPTA20250107004'),
(N'DMSBOK20250108011'),
(N'DMSCPT20250108019'),
(N'DMSDBN20250108008'),
(N'DMSBOK20250107009'),
(N'DMSDBN20250108009'),
(N'DMSDBN20250108003'),
(N'DMSCPT20250107008'),
(N'DMSJNR20250108011'),
(N'DMSDBN20250108007'),
(N'DMSDBN20250108010'),
(N'DMSBFN20250108004'),
(N'DMSBFN20250108003'),
(N'DMSPTA20250108006'),
(N'DMSKEM20250108001'),
(N'DMSDBN20250108014'),
(N'DMSBOK20250107017'),
(N'DMSDBN20250108016'),
(N'DMSBOK20250109001'),
(N'DMSBOK20250108010'),
(N'DMSPTA20250109005'),
(N'DMSJNR20250108008'),
(N'DMSJNR20250108013'),
(N'DMSDBN20250109002'),
(N'DMSJNR20250109004'),
(N'DMSJNR20250108012'),
(N'DMSPTA20250106002'),
(N'DMSBOK20250109008'),
(N'DMSPTA20250109007'),
(N'DMSBOK20250109002'),
(N'DMSBOK20250109005'),
(N'DMSCPT20250109009'),
(N'DMSJNR20250109005'),
(N'DMSBOK20250108008'),
(N'DMSCPT20241218016'),
(N'DMSDBN20250107009'),
(N'DMSPEB20250109001'),
(N'DMSJNR20250109001'),
(N'DMSCPT20250109004'),
(N'DMSBFN20250109002'),
(N'DMSJNR20250107009'),
(N'DMSJNR20250114015'),
(N'DMSJNR20250115010'),
(N'DMSMUL20250115001'),
(N'DMSCPT20250114028'),
(N'DMSCPT20250115012'),
(N'DMSJNR20250114021'),
(N'DMSDBN20250114013'),
(N'DMSJNR20250115016'),
(N'DMSPEB20250113004'),
(N'DMSJNR20250115015'),
(N'DMSPTA20250115012'),
(N'DMSCPT20250115022'),
(N'DMSCPT20250115016'),
(N'DMSCPT20250115009'),
(N'DMSDBN20250115009'),
(N'DMSPEB20250115003'),
(N'DMSPTA20250110006'),
(N'DMSCPT20250114023'),
(N'DMSPTA20250115013'),
(N'DMSBFN20250115007'),
(N'DMSCPT20250115020'),
(N'DMSCPT20250115017'),
(N'DMSBOK20250113010'),
(N'DMSMUL20250115002'),
(N'DMSCPT20250115021'),
(N'DMSJNR20250115020'),
(N'DMSDBN20250115007'),
(N'DMSJNR20250113002'),
(N'DMSPTA20250115011'),
(N'DMSCPT20250114029'),
(N'DMSBFN20250115002'),
(N'DMSDBN20250115013'),
(N'DMSBFN20250115006'),
(N'DMSBFN20250115011'),
(N'DMSCPT20250115027'),
(N'DMSBFN20250115010'),
(N'DMSKEM20250115001'),
(N'DMSBOK20250115013'),
(N'DMSJNR20241206005'),
(N'DMSCPT20250115024'),
(N'DMSDBN20250115005'),
(N'DMSDBN20250115019'),
(N'DMSPTA20250113022'),
(N'DMSPTA20250114010'),
(N'DMSPTA20250115022'),
(N'DMSBOK20250115006'),
(N'DMSPTA20250115018'),
(N'DMSDBN20250113017'),
(N'DMSBFN20250115015'),
(N'DMSPEB20250113002'),
(N'DMSCPT20250115029'),
(N'DMSJNR20250115023'),
(N'DMSDBN20250115017'),
(N'DMSBOK20250114014'),
(N'DMSBOK20250115017'),
(N'DMSPTA20250115028'),
(N'DMSCPT20250115013'),
(N'DMSCPT20250113010'),
(N'DMSJNR20250115022'),
(N'DMSBOK20250115012'),
(N'DMSBOK20250115014'),
(N'DMSCPT20250115001'),
(N'DMSDBN20241218001'),
(N'DMSDBN20250115021'),
(N'DMSCPT20250115026'),
(N'DMSCPT20250114033'),
(N'DMSPTA20250115014'),
(N'DMSPTA20250115008'),
(N'DMSDBN20250114001'),
(N'DMSPTA20250109016'),
(N'DMSBOK20250109012'),
(N'DMSCPT20241223021'),
(N'DMSDBN20250114018'),
(N'DMSJNR20250115026'),
(N'DMSDBN20250115022'),
(N'DMSBFN20250106001'),
(N'DMSPTA20250116003'),
(N'DMSPTA20250120009'),
(N'DMSCPT20250114030'),
(N'DMSCPT20250120011'),
(N'DMSBOK20250120023'),
(N'DMSCPT20250120031'),
(N'DMSBOK20250115009'),
(N'DMSJNR20250121006'),
(N'DMSDBN20250120014'),
(N'DMSJNR20250120016'),
(N'DMSCPT20250121003'),
(N'DMSPTA20250120006'),
(N'DMSCPT20250117006'),
(N'DMSDBN20250120008'),
(N'DMSPTA20250121001'),
(N'DMSCPT20250121008'),
(N'DMSDBN20250121003'),
(N'DMSGAR20250120003'),
(N'DMSDBN20250121002'),
(N'DMSBOK20250121008'),
(N'DMSBOK20250121004'),
(N'DMSEDE20250121001'),
(N'DMSDBN20250120022'),
(N'DMSPEB20250120007'),
(N'DMSCPT20241218013'),
(N'DMSPEB20250121001'),
(N'DMSCPT20250120030'),
(N'DMSJNR20241219012'),
(N'DMSJNR20250120015'),
(N'DMSCPT20250120018'),
(N'DMSJNR20250120021'),
(N'DMSPTA20250120022'),
(N'DMSPTA20250121004'),
(N'DMSDBN20250115002'),
(N'DMSCPT20250120033'),
(N'DMSBFN20250121002'),
(N'DMSDBN20250120015'),
(N'DMSBOK20250121010'),
(N'DMSCPT20250121002'),
(N'DMSJNR20250121001'),
(N'DMSBOK20250121011'),
(N'DMSCPT20250114008'),
(N'DMSBOK20250120024'),
(N'DMSDBN20250121007'),
(N'DMSBOK20250120007'),
(N'DMSCPT20250121017'),
(N'DMSPTA20250117022'),
(N'DMSCPT20250121006'),
(N'DMSPTA20250121006'),
(N'DMSMUL20250121001'),
(N'DMSBOK20250121006'),
(N'DMSDBN20250121006'),
(N'DMSBFN20250120006'),
(N'DMSCPT20250121013'),
(N'DMSBOK20250121003'),
(N'DMSPTA20250121008'),
(N'DMSBOK20250121012'),
(N'DMSPTA20250121005'),
(N'DMSPTA20250121003'),
(N'DMSPTA20250121007'),
(N'DMSBOK20250120013'),
(N'DMSKEM20250120002'),
(N'DMSCPT20250115030'),
(N'DMSPTA20250121010'),
(N'DMSBOK20250120014'),
(N'DMSCPT20250121029'),
(N'DMSJNR20250121013'),
(N'DMSPTA20250121002'),
(N'DMSPTA20241211014'),
(N'DMSBOK20250121013'),
(N'DMSCPT20250116023'),
(N'DMSCPT20250121010'),
(N'DMSJNR20250121010'),
(N'DMSDBN20250123001'),
(N'DMSCPT20250123040'),
(N'DMSPTA20250123025'),
(N'DMSBOK20250122030'),
(N'DMSPTA20250123017'),
(N'DMSBFN20250114005'),
(N'DMSBOK20250123026'),
(N'DMSPTA20250124003'),
(N'DMSDBN20250123019'),
(N'DMSJNR20250123028'),
(N'DMSJNR20250123012'),
(N'DMSPTA20250124004'),
(N'DMSJNR20250123027'),
(N'DMSCPT20250121016'),
(N'DMSJNR20250122015'),
(N'DMSCPT20250124002'),
(N'DMSDBN20250123016'),
(N'DMSBOK20250123023'),
(N'DMSDBN20250124002'),
(N'DMSPTA20250124007'),
(N'DMSBOK20250124010'),
(N'DMSCPT20250123039'),
(N'DMSBOK20250124009'),
(N'DMSBOK20250124011'),
(N'DMSDBN20250124004'),
(N'DMSPTA20250124001'),
(N'DMSJNR20250123024'),
(N'DMSBOK20250117005'),
(N'DMSBOK20250123017'),
(N'DMSCPT20250123031'),
(N'DMSJNR20250124002'),
(N'DMSBOK20250124016'),
(N'DMSJNR20250120012'),
(N'DMSPEB20250124001'),
(N'DMSCPT20241210013'),
(N'DMSCPT20250124004'),
(N'DMSPTA20250124009'),
(N'DMSBOK20250124013'),
(N'DMSDBN20250124005'),
(N'DMSCPT20250123028'),
(N'DMSBOK20250124012'),
(N'DMSPTA20250124011'),
(N'DMSBOK20250114009'),
(N'DMSBOK20250124017'),
(N'DMSBOK20250124019'),
(N'DMSJNR20250123025'),
(N'DMSJNR20250124004'),
(N'DMSBOK20250124015'),
(N'DMSCPT20250124010'),
(N'DMSJNR20250122029'),
(N'DMSPTA20250122004'),
(N'DMSJNR20250123005'),
(N'DMSJNR20250123013'),
(N'DMSBFN20250124002'),
(N'DMSJNR20250123007'),
(N'DMSCPT20250124003'),
(N'DMSPEB20250124002'),
(N'DMSDBN20250116026'),
(N'DMSCPT20250124018'),
(N'DMSBOK20250124006'),
(N'DMSKEM20250124002'),
(N'DMSJNR20250124010'),
(N'DMSCPT20250123006'),
(N'DMSDBN20250124003'),
(N'DMSPEB20250122001'),
(N'DMSCPT20250124017'),
(N'DMSBOK20250124018'),
(N'DMSJNR20250124007'),
(N'DMSCPT20250124005'),
(N'DMSBFN20250128007'),
(N'DMSJNR20250128015'),
(N'DMSCPT20250128024'),
(N'DMSBOK20250123019'),
(N'DMSJNR20250128033'),
(N'DMSBOK20250128031'),
(N'DMSBOK20250128020'),
(N'DMSPTA20250127024'),
(N'DMSBOK20250128032'),
(N'DMSBOK20250128030'),
(N'DMSBOK20250128035'),
(N'DMSPEB20250128012'),
(N'DMSBOK20250128034'),
(N'DMSBOK20250128025'),
(N'DMSJNR20250114017'),
(N'DMSCPT20250128033'),
(N'DMSPTA20250128019'),
(N'DMSJNR20250128030'),
(N'DMSPTA20250128016'),
(N'DMSPEB20250128011'),
(N'DMSJNR20250128031'),
(N'DMSCPT20241210002'),
(N'DMSDBN20250128016'),
(N'DMSPTA20250129002'),
(N'DMSCPT20250128029'),
(N'DMSEDE20250129001'),
(N'DMSPTA20250123015'),
(N'DMSDBN20250124016'),
(N'DMSJNR20250127008'),
(N'DMSDBN20250127012'),
(N'DMSBOK20250128027'),
(N'DMSJNR20250129006'),
(N'DMSPTA20250129005'),
(N'DMSDBN20250129004'),
(N'DMSCPT20250128026'),
(N'DMSDBN20250127022'),
(N'DMSPTA20250127001'),
(N'DMSBOK20250128010'),
(N'DMSPEB20250128005'),
(N'DMSJNR20250129009'),
(N'DMSCPT20250127023'),
(N'DMSCPT20250128021'),
(N'DMSDBN20250128017'),
(N'DMSBOK20250128022'),
(N'DMSBFN20250129004'),
(N'DMSBFN20250129001'),
(N'DMSPTA20250129013'),
(N'DMSBFN20250130010'),
(N'DMSCPT20250124012'),
(N'DMSCPT20250131023'),
(N'DMSPEB20250129005'),
(N'DMSJNR20250131014'),
(N'DMSDBN20250131010'),
(N'DMSBFN20250130004'),
(N'DMSBFN20250131011'),
(N'DMSPTA20250131013'),
(N'DMSBOK20250130011'),
(N'DMSBOK20250131011'),
(N'DMSCPT20250129043'),
(N'DMSDBN20250131006'),
(N'DMSPTA20250130017'),
(N'DMSPTA20250128021'),
(N'DMSCPT20250131020'),
(N'DMSCPT20250131030'),
(N'DMSCPT20250131031'),
(N'DMSDBN20250131011'),
(N'DMSBOK20250130005'),
(N'DMSBOK20250117008'),
(N'DMSBOK20250131017'),
(N'DMSDBN20250131012'),
(N'DMSBOK20250122016'),
(N'DMSBFN20250131012'),
(N'DMSBOK20250131016'),
(N'DMSCPT20250131028'),
(N'DMSPTA20250131016'),
(N'DMSPTA20250131017'),
(N'DMSJNR20250131021'),
(N'DMSPTA20250131001'),
(N'DMSKIM20250131001'),
(N'DMSBOK20250131019'),
(N'DMSPTA20250131015'),
(N'DMSJNR20250129036'),
(N'DMSCPT20250131013'),
(N'DMSBFN20250131013'),
(N'DMSBOK20250131021'),
(N'DMSJNR20250131018'),
(N'DMSJNR20250131025'),
(N'DMSJNR20250131022'),
(N'DMSCPT20250130043'),
(N'DMSBOK20250201001'),
(N'DMSBOK20250131008'),
(N'DMSBOK20250201002'),
(N'DMSBOK20250128007'),
(N'DMSPTA20250131020'),
(N'DMSPTA20250131019'),
(N'DMSPTA20250131018'),
(N'DMSCPT20250123004'),
(N'DMSJNR20250131024'),
(N'DMSBOK20250203003'),
(N'DMSCPT20250203007'),
(N'DMSDBN20250131014'),
(N'DMSDBN20250131013'),
(N'DMSCPT20250203005'),
(N'DMSCPT20250203003'),
(N'DMSBOK20250203005'),
(N'DMSDBN20250123005'),
(N'DMSCPT20250129024'),
(N'DMSCPT20250203006'),
(N'DMSBOK20250205032'),
(N'DMSCPT20250205028'),
(N'DMSDBN20250204002'),
(N'DMSCPT20250205010'),
(N'DMSCPT20250129034'),
(N'DMSJNR20250203014'),
(N'DMSPEB20250205001'),
(N'DMSCPT20250204012'),
(N'DMSPTA20250109004'),
(N'DMSDBN20250128014'),
(N'DMSCPT20250205031'),
(N'DMSCPT20250128025'),
(N'DMSCPT20250205042'),
(N'DMSPTA20250205014'),
(N'DMSBFN20250205015'),
(N'DMSPTA20250205016'),
(N'DMSDBN20250130011'),
(N'DMSBOK20250206006'),
(N'DMSCPT20250205032'),
(N'DMSBOK20250129027'),
(N'DMSPTA20250206004'),
(N'DMSKEM20250206001'),
(N'DMSCPT20250205020'),
(N'DMSCPT20250206004'),
(N'DMSDBN20250206004'),
(N'DMSBOK20250205033'),
(N'DMSPTA20250205015'),
(N'DMSKEM20250205003'),
(N'DMSBFN20250205017'),
(N'DMSBOK20250206007'),
(N'DMSBOK20250128011'),
(N'DMSPTA20250206001'),
(N'DMSBOK20250205025'),
(N'DMSBOK20250206002'),
(N'DMSJNR20250206010'),
(N'DMSCPT20250205037'),
(N'DMSMUL20250206001'),
(N'DMSBOK20250205026'),
(N'DMSJNR20250206005'),
(N'DMSCPT20250204048'),
(N'DMSJNR20250206002'),
(N'DMSKEM20250203001'),
(N'DMSBOK20250206008'),
(N'DMSPTA20250205019'),
(N'DMSBOK20250206010'),
(N'DMSJNR20250204019'),
(N'DMSBOK20250206009'),
(N'DMSPTA20250206002'),
(N'DMSBOK20250210040'),
(N'DMSJNR20250210021'),
(N'DMSJNR20250211005'),
(N'DMSJNR20250210009'),
(N'DMSCPT20250211017'),
(N'DMSCPT20250210001'),
(N'DMSBOK20250211008'),
(N'DMSCPT20250211018'),
(N'DMSCPT20250211015'),
(N'DMSBOK20250210046'),
(N'DMSBFN20250211006'),
(N'DMSCPT20250210038'),
(N'DMSJNR20250211011'),
(N'DMSPTA20250211008'),
(N'DMSCPT20250210034'),
(N'DMSEDE20250211001'),
(N'DMSCPT20250207006'),
(N'DMSMUL20250211001'),
(N'DMSPTA20250203016'),
(N'DMSDBN20250211006'),
(N'DMSBOK20250211009'),
(N'DMSPEB20250207004'),
(N'DMSBOK20250211010'),
(N'DMSJNR20250211018'),
(N'DMSPTA20250210019'),
(N'DMSPTA20250211002'),
(N'DMSJNR20250211006'),
(N'DMSCPT20250211019'),
(N'DMSBFN20250210011'),
(N'DMSPTA20250211016'),
(N'DMSBFN20250211004'),
(N'DMSBFN20250210010'),
(N'DMSPTA20250211009')
;
GO

--4
---------------------------------------
CREATE NONCLUSTERED INDEX IX_#TriggerPatterns_Pattern
    ON #TriggerPatterns (Pattern);
GO

--5
----------------------------------------

SELECT 
      mt.[LeadId]
    , mt.[Status]
    , mt.[InitialProjectedLeadStatus]
    , mt.[BlgProfileId]
    , mt.[Context]
    , mt.[LeadRequestType]
    , mt.[Product]
    , mt.[LeadGenerationRuleId]
    , mt.[LeadGenerationRuleDescription]
    , mt.[TriggerRecordId]
    , mt.[DateRule]
    , mt.[DateRulePeriod]
    , mt.[CampaignList]
    , mt.[SourceDate]
    , mt.[LeadDate]
    , mt.[ProcessedDate]
    , mt.[IsAnniversaryLead]
    , mt.[ProcessingComment]
    , mt.[LeadExclusionRuleId]
    , mt.[LeadExclusionRuleDescription]
    , mt.[Initials]
    , mt.[FirstName]
    , mt.[Surname]
    , mt.[IDNumber]
    , mt.[ApplicantType]
    , mt.[CellPhoneNumber]
    , mt.[WorkPhoneNumber]
    , mt.[HomePhoneNumber]
    , mt.[EmailAddress]
    , mt.[DateOfBirth]
    , mt.[Gender]
    , mt.[HomeLanguage]
    , mt.[MaritalStatus]
    , mt.[EthnicGroup]
    , mt.[HighestLevelOfEducation]
    , mt.[EmploymentStatus]
    , mt.[GrossIncome]
    , mt.[Source]
    , mt.[SourceSystem]
    , mt.[SourceSystemId]
    , mt.[AdditionalInfo]
    , mt.[CreatedDate]
    , mt.[StatusClassId]
    , mt.[Title]
    , mt.[ExternalLeadId]
    , mt.[LeadSubject]
    , mt.[LeadComments]
    , mt.[IsDeleted]
    , mt.[UpdatedDate]
    , mt.[LeadCreatedDate]
    , mt.[SiphonLeadId]
    , mt.[StagingLeadId]
    , mt.[ReadyForProcessingDate]
    , mt.[SourceMarketingCommsId]
    , mt.[CampaignId]
    , mt.[MediaSource]
    , mt.[SnowReportEventDate]
    , mt.[DedupeRule]
    , mt.[DedupeFromDate]
    , mt.[SalesFunction]
    , mt.[SourcePolicyNumber]
    , mt.[CreditCheckColor]
FROM dbo.Lead_Lead AS mt WITH (NOLOCK)
INNER JOIN #TriggerPatterns AS p
    ON mt.TriggerRecordId LIKE p.Pattern + N'%'
-- optional extra filters, e.g.:
 -- mt.Status = 'Success'
OPTION (RECOMPILE);
GO

--5
------------------------------

DROP TABLE #TriggerPatterns;
GO
# Product Requirements Document (PRD)
## Intake Leads Campaign Creation System

### Document Information
- **Version**: 1.0
- **Date**: July 28, 2025
- **Author**: Data Analytics Team
- **Status**: Draft

---

## 1. Executive Summary

### 1.1 Problem Statement
After processing intake submissions through `intake2.py`, we have identified prospects where **no leads were created** (`LeadCreated = 'No'`). These represent missed opportunities that require targeted follow-up campaigns. Currently, there's no systematic approach to convert these failed intake attempts into new lead generation opportunities.

### 1.2 Solution Overview
Develop an automated campaign creation system that:
- Filters intake data for prospects without leads
- Creates targeted "Intake Leads" campaigns
- Generates specialized "Commercial Leads" sub-campaigns
- Applies intelligent lead creation logic based on bank decline patterns
- Implements strategic date lagging for optimal timing

---

## 2. Current State Analysis

### 2.1 Data Source
- **Input File**: `intake_for_nb.xlsx` (output from intake2.py)
- **Key Filter**: `LeadCreated = 'No'`
- **Available Data**: Bank decline patterns, submission types, intake dates

### 2.2 Business Opportunity
Prospects with `LeadCreated = 'No'` represent untapped potential for:
- Home Owners Cover (HOC) leads
- BetterLife Protector (BLP) leads
- Commercial insurance opportunities

---

## 3. Requirements

### 3.1 Functional Requirements

#### 3.1.1 Data Filtering (Priority: High)
- **FR-001**: System shall filter `intake_for_nb.xlsx` for records where `LeadCreated = 'No'`
- **FR-002**: System shall validate data quality before processing
- **FR-003**: System shall handle missing or null values gracefully

#### 3.1.2 Campaign Creation (Priority: High)
- **FR-004**: System shall create "Intake Leads" campaign from filtered prospects
- **FR-005**: System shall generate unique campaign identifiers and timestamps
- **FR-006**: System shall maintain audit trail of campaign creation

#### 3.1.3 Commercial Leads Sub-Campaign (Priority: High)
- **FR-007**: System shall identify Commercial prospects based on SubmissionType:
  - Closed Corporation
  - Trust
  - Public Company
  - Private Company
  - Non-profit Organization
  - Incorporated Company
- **FR-008**: System shall create "Commercial Leads" sub-campaign from identified prospects
- **FR-009**: System shall maintain parent-child relationship between campaigns

#### 3.1.4 Lead Creation Logic (Priority: High)
- **FR-010**: **All Banks Decline Scenario**
  - IF all banks in `DeclinedBanks` list declined AND `SuccessfulBanks` is empty
  - THEN create HOC lead only
- **FR-011**: **Partial Bank Decline Scenario**
  - IF one or more banks declined BUT some banks were successful
  - THEN create both HOC and BLP leads
- **FR-012**: System shall validate bank decline data before applying logic

#### 3.1.5 Date Management (Priority: High)
- **FR-013**: System shall lag intake date by 1 month for campaign leads
- **FR-014**: System shall handle month-end edge cases (e.g., Jan 31 → Feb 28)
- **FR-015**: System shall maintain original intake date for reference

#### 3.1.6 Output Generation (Priority: High)
- **FR-016**: System shall generate campaign lead files in required format
- **FR-017**: System shall include all necessary fields for lead processing
- **FR-018**: System shall export to Excel format for downstream systems

### 3.2 Non-Functional Requirements

#### 3.2.1 Performance
- **NFR-001**: Process 10,000 intake records within 30 seconds
- **NFR-002**: Generate campaign files within 60 seconds

#### 3.2.2 Data Quality
- **NFR-003**: Achieve 99.9% data accuracy in lead creation logic
- **NFR-004**: Validate 100% of bank decline patterns before processing

---

## 4. Technical Specification

### 4.1 System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ intake_for_nb   │    │   Campaign       │    │  Lead Creation  │
│    .xlsx        │───▶│   Filter         │───▶│     Engine      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Commercial      │    │  Campaign       │
                       │  Classifier      │    │  Output Files   │
                       └──────────────────┘    └─────────────────┘
```

### 4.2 Core Components

#### 4.2.1 CampaignProcessor Class
```python
class CampaignProcessor:
    def __init__(self, input_file: str = "intake_for_nb.xlsx")
    def filter_no_leads(self) -> pd.DataFrame
    def identify_commercial_leads(self, df: pd.DataFrame) -> pd.DataFrame
    def apply_lead_creation_logic(self, df: pd.DataFrame) -> pd.DataFrame
    def lag_intake_dates(self, df: pd.DataFrame) -> pd.DataFrame
    def generate_campaigns(self) -> Dict[str, pd.DataFrame]
```

#### 4.2.2 Lead Creation Logic Implementation
```python
def determine_lead_types(row):
    successful_banks = row['SuccessfulBanks']
    declined_banks = row['DeclinedBanks']
    
    # All banks declined
    if not successful_banks and declined_banks:
        return ['HOC']
    
    # Some banks declined, some successful
    elif successful_banks and declined_banks:
        return ['HOC', 'BLP']
    
    # Edge case handling
    else:
        return []
```

### 4.3 Data Flow

#### 4.3.1 Input Processing
1. Load `intake_for_nb.xlsx`
2. Filter for `LeadCreated = 'No'`
3. Validate data completeness

#### 4.3.2 Campaign Classification
1. **Main Campaign**: All filtered prospects → "Intake Leads"
2. **Sub-Campaign**: Commercial SubmissionTypes → "Commercial Leads"

#### 4.3.3 Lead Generation
1. Analyze bank decline patterns
2. Apply lead creation logic
3. Generate HOC and/or BLP leads
4. Lag dates by 1 month

#### 4.3.4 Output Generation
1. Create campaign lead files
2. Include metadata and timestamps
3. Export to Excel format

---

## 5. Business Rules

### 5.1 Commercial Lead Identification
```python
COMMERCIAL_SUBMISSION_TYPES = [
    'Closed Corporation',
    'Trust', 
    'Public Company',
    'Private Company',
    'Non-profit Organization',
    'Incorporated Company'
]
```

### 5.2 Lead Creation Matrix
| Bank Decline Pattern | Successful Banks | Declined Banks | Lead Types Created |
|---------------------|------------------|----------------|-------------------|
| All Declined        | Empty/None       | Has Values     | HOC Only          |
| Partial Decline     | Has Values       | Has Values     | HOC + BLP         |
| No Data             | Empty/None       | Empty/None     | No Leads          |

### 5.3 Date Lagging Rules
- **Standard**: Add 1 month to `LeadIntakeDate`
- **Month-end handling**: If original date is month-end, use last day of target month
- **Example**: 2025-01-31 → 2025-02-28 (not 2025-03-03)

---

## 6. Output Specifications

### 6.1 Campaign Lead File Structure
| Field | Description | Source |
|-------|-------------|--------|
| CampaignId | Unique campaign identifier | Generated |
| IdNumber | Prospect ID | intake_for_nb.xlsx |
| BusinessEntity | Company type | intake_for_nb.xlsx |
| SubmissionType | Legal entity type | intake_for_nb.xlsx |
| OriginalIntakeDate | Original submission date | intake_for_nb.xlsx |
| CampaignDate | Lagged campaign date | Calculated |
| LeadTypes | HOC, BLP, or both | Calculated |
| DeclineReason | Bank decline summary | Derived |
| CampaignType | Main/Commercial | Classified |

### 6.2 File Naming Convention
- **Main Campaign**: `Intake_Leads_YYYYMMDD.xlsx`
- **Commercial Sub-Campaign**: `Commercial_Leads_YYYYMMDD.xlsx`

---

## 7. Success Criteria

### 7.1 Acceptance Criteria
- [ ] Successfully filter prospects with no leads created
- [ ] Correctly identify commercial vs. individual prospects
- [ ] Apply lead creation logic with 100% accuracy
- [ ] Generate properly formatted campaign files
- [ ] Implement date lagging correctly
- [ ] Maintain data integrity throughout process

### 7.2 Key Performance Indicators
- **Conversion Rate**: % of intake prospects converted to campaign leads
- **Commercial Identification**: Accuracy of commercial lead classification
- **Processing Time**: Time to generate campaigns from intake data
- **Data Quality**: Error rate in lead creation logic

---

## 8. Implementation Plan

### 8.1 Phase 1: Core Development (Week 1)
- Develop filtering and classification logic
- Implement lead creation rules
- Create date lagging functionality

### 8.2 Phase 2: Integration & Testing (Week 2)
- Integrate with intake_for_nb.xlsx processing
- Comprehensive testing of business rules
- Validate output file formats

### 8.3 Phase 3: Deployment & Monitoring (Week 3)
- Production deployment
- Performance monitoring
- User training and documentation

---

## 9. Risk Assessment

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Incorrect lead creation logic | High | Medium | Extensive testing, business rule validation |
| Date calculation errors | Medium | Low | Edge case testing, date library usage |
| Data quality issues | High | Medium | Input validation, error handling |

---

**Next Steps**: Technical review, business rule validation, and development kickoff

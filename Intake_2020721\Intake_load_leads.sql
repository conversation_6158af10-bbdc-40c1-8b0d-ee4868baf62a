SET NOCOUNT ON;
DECLARE @BatchSize INT = 1000,
        @Offset    INT = 0;

WHILE 1=1
BEGIN
    ;WITH FilteredSubmissions AS (
        SELECT
            a.IDNumber,
            a.Title,
            a.Initials,
            a.FirstName,
            a.Surname,
            a.Date<PERSON>fBirth,
            COALESCE(
          NULLIF(LTRIM(RTRIM(a.MobilePhoneNumber)),''), 
          a.WorkPhoneNumber
        )    AS CellPhoneNumber,
            a.WorkPhoneNumber,
            NULL                    AS HomePhoneNumber,
            a.<PERSON>dd<PERSON>,
            CASE 
                WHEN CHARINDEX('_', s.SubRef) > 0  
                    THEN LEFT(s.SubRef, CHARINDEX('_', s.SubRef) - 1)
                ELSE
                    s.SubRef
            END                    AS ApplicationReference,
            s.PurchasePrice,
            s.LoanAmount,
            s.BankName             AS Bank,
            s.BondAttorney,
            s.Region,
            s.Consultant           AS ConsultantName,
            s.ConsultantId,
            s.PropertyDescription,
            NULL                   AS AddressLine1,
            NULL                   AS AddressLine2,
            NULL                   AS City,
            NULL                   AS Suburb,
            NULL                   AS Province,
            NULL                   AS PostalCode,
            NULL                   AS IsNewDevelopment,
            s.LoanType,
            NULL                   AS PropertyType,
            s.BusinessEntity,
            a.Gender,
            a.HomeLanguage,
            a.MaritalStatus,
            a.EthnicGroup,
            a.HighestLevelOfEducation,
            a.EmploymentStatus,
            a.GrossIncome,
            s.LastStatusUpdate
        FROM [DW_STG_MO].[dbo].[Submissions] s WITH (NOLOCK)
        JOIN [DW_STG_MO].[dbo].[Applicants] a WITH (NOLOCK)
          ON a.SubId = s.SubId
        WHERE s.StatusWorkflow <> 'Granted'
		and IDNumber in (
		'6211030112087',
'8806056014089',
'9504110034081',
'9511235112080',
'0003290443088',
'7209056363187',
'8804155594085',
'9812230491085',
'6103155062082',
'6507195015084',
'6601020111083',
'6702215018081',
'6705160101083',
'6904120271082',
'7011040466088',
'7103275325087',
'7208115971188',
'7304195146088',
'7308245151089',
'7502145958086',
'7508150407083',
'7510160252087',
'7601130335084',
'7605195061089',
'7710270235085',
'7711015782084',
'7805145273087',
'7812165553083',
'7901010218081',
'7902035313089',
'7906165997082',
'7906210541083',
'8002286112089',
'8006126025088',
'8009100529083',
'8010270036081',
'8103036105082',
'8109035562082',
'8110245539084',
'8112180389087',
'8112195339085',
'8201275095087',
'8203085163089',
'8209225416089',
'8302260026085',
'8307190286089',
'8308205143083',
'8309135891080',
'8402080072085',
'8405220763083',
'8409201007080',
'8410085067083',
'8501060862082',
'8504135726084',
'8506110306089',
'8510115715088',
'8601200017082',
'8602180011087',
'8607075191084',
'8608165704083',
'8612130603081',
'8706235219080',
'8710175328083',
'8712200412080',
'8804160751084',
'8805010237083',
'8806235092089',
'8807255536088',
'8810115090080',
'8811255284087',
'8812020187084',
'8905085480087',
'8907185263082',
'8909215202080',
'9005010556082',
'9008145099085',
'9008300209081',
'9101216183084',
'9104210127081',
'9105031009085',
'9105290920089',
'9106055450080',
'9106230552081',
'9108150736082',
'9111125125084',
'9112015978087',
'9112225635089',
'9203030686084',
'9207030946083',
'9207255275085',
'9209225360088',
'9211050261084',
'9306210030088',
'9307260280086',
'9309055379081',
'9310050270085',
'9401036519180',
'9401150269083',
'9402215185082',
'9402270322083',
'9406215123084',
'9407315048080',
'9409215650080',
'9410120495082',
'9412170223083',
'9501110352083',
'9501276030085',
'9503130447083',
'9512045744088',
'9611165814083',
'9703066110081',
'9802120513080',
'9805165110084',
'9807230327081',
'9812215120089',
'9906280086082',
'9912105275082',
'0005225205086',
'0105155230086',
'6307265223183',
'6309025394080',
'6506130354087',
'6511185168085',
'6901015020087',
'6910070439082',
'7108090153085',
'7408206206185',
'7408280264084',
'7603255804084',
'7609155152086',
'7611065438084',
'7703215449085',
'7708060028082',
'7802045098086',
'7806090095087',
'7806140206080',
'7901215372089',
'7902190025080',
'7911255226087',
'8007050404083',
'8008125610084',
'8108085033085',
'8112045841082',
'8210095598089',
'8210165208080',
'8211040684081',
'8306300837088',
'8307170264080',
'8311210045087',
'8312255014087',
'8405260603082',
'8408060235089',
'8408235066088',
'8410125120082',
'8501240728088',
'8505300637089',
'8506190472082',
'8506245055080',
'8508090825088',
'8508306242086',
'8510280139080',
'8511045096086',
'8602270728087',
'8603125139082',
'8604245811089',
'8612120926088',
'8702240862089',
'8703175124081',
'8708135042083',
'8803220374085',
'8804305535087',
'8805036071086',
'8808180023085',
'8808190139087',
'8812030135081',
'8901045229086',
'8908135632087',
'8908250003080',
'8910290505082',
'8911305205080',
'9009091139081',
'9108085016089',
'9109155566086',
'9110075279081',
'9203065049083',
'9205211004086',
'9207115157085',
'9209156597088',
'9302026221086',
'9310215253083',
'9311235063080',
'9401085106087',
'9408155886084',
'9502175312087',
'9502250054083',
'9503135699084',
'9504255202089',
'9505015950081',
'9507300809089',
'9602295549087',
'9605020811086',
'9701020121087',
'9708055060080',
'9710100684088',
'9809255804085',
'9811160125085',
'9906130107088',
'0011255026087',
'0109020414081',
'0311250800084',
'6107075057080',
'6303210100089',
'6303275236083',
'6312210560085',
'6408246097082',
'6510185060086',
'6512065163089',
'6605205677081',
'6605265079087',
'6808125048082',
'7003205184085',
'7005070511086',
'7005280079080',
'7301275111081',
'7311300787086',
'7403215213082',
'7411245815081',
'7601045030085',
'7601160646087',
'7602175097084',
'7605110109088',
'7804305168088',
'7809205453084',
'8001150995082',
'8007020025083',
'8011160095088',
'8102180817088',
'8108205021085',
'8202145176081',
'8203215166085',
'8211190251087',
'8212070661080',
'8212125712086',
'8301295503084',
'8304225536083',
'8305060687089',
'8307150509082',
'8309185947089',
'8312285883089',
'8402240123083',
'8404150731087',
'8406066485088',
'8410060542084',
'8411150129089',
'8412050729080',
'8412050729085',
'8412095614086',
'8502240609088',
'8504265023088',
'8506030054082',
'8506180361089',
'8506225562089',
'8608190130080',
'8612160131086',
'8704265262088',
'8705220383083',
'8706140749080',
'8709210822084',
'8710096161084',
'8812016401184',
'8902035120087',
'8902125072081',
'8908285377087',
'8909255557088',
'8911275035087',
'8912065559088',
'9001265396084',
'9006205390089',
'9006226218087',
'9007285404089',
'9010190860080',
'9103286122083',
'9106100798087',
'9108155323084',
'9111285104085',
'9201025169082',
'9204110089082',
'9208175361088',
'9210280325081',
'9301215229082',
'9304225171088',
'9304295087081',
'9306090019086',
'9307075231084',
'9308086438189',
'9308260144082',
'9309255015089',
'9310086287087',
'9312035367085',
'9404130011088',
'9404160338088',
'9408015094085',
'9409130632080'

		
		)
    ),
    LatestPerID AS (
        SELECT *,
               ROW_NUMBER() 
                 OVER (PARTITION BY IDNumber 
                       ORDER BY LastStatusUpdate DESC) AS rn
        FROM FilteredSubmissions
    ),
    NumberedRecords AS (
        SELECT
            ROW_NUMBER() OVER (ORDER BY IDNumber) AS RowNum,
            IDNumber,
            Title,
            Initials,
            FirstName,
            Surname,
            DateOfBirth,
            CellPhoneNumber,
            WorkPhoneNumber,
            HomePhoneNumber,
            EmailAddress,
            ApplicationReference,
            PurchasePrice,
            LoanAmount,
            Bank,
            BondAttorney,
            Region,
            ConsultantName,
            ConsultantId,
            PropertyDescription,
            AddressLine1,
            AddressLine2,
            City,
            Suburb,
            Province,
            PostalCode,
            IsNewDevelopment,
            LoanType,
            PropertyType,
            BusinessEntity,
            Gender,
            HomeLanguage,
            MaritalStatus,
            EthnicGroup,
            HighestLevelOfEducation,
            EmploymentStatus,
            GrossIncome
        FROM LatestPerID
        WHERE rn = 1
        ORDER BY IDNumber
        OFFSET @Offset ROWS 
        FETCH NEXT @BatchSize ROWS ONLY
    )

    --INSERT INTO OPENQUERY(
    --    [HO-VSSQL-SVR01],
    --    'SELECT
    --         [Lead_LeadDate],
    --         [Lead_ReadyForProcessingDate],
    --         [Lead_Product],
    --         [Details_IDNumber],
    --         [Details_Title],
    --         [Details_Initials],
    --         [Details_FirstName],
    --         [Details_Surname],
    --         [Details_DateOfBirth],
    --         [Details_CellPhoneNumber],
    --         [Details_WorkPhoneNumber],
    --         [Details_HomePhoneNumber],
    --         [Details_EmailAddress],
    --         [Bond_ApplicationReference],
    --         [Bond_PurchasePrice],
    --         [Bond_LoanAmount],
    --         [Bond_Bank],
    --         [Bond_BondAttorney],
    --         [Entity_Region],
    --         [Consultant_FirstName],
    --         [Consultant_ConsultantId],
    --         [Property_PropertyDescription],
    --         [Property_AddressLine1],
    --         [Property_AddressLine2],
    --         [Property_City],
    --         [Property_Suburb],
    --         [Property_Province],
    --         [Property_PostalCode],
    --         [Property_IsNewDevelopment],
    --         [Bond_LoanType],
    --         [Property_PropertyType],
    --         [Entity_BusinessEntity],
    --         [Details_Gender],
    --         [Details_HomeLanguage],
    --         [Details_MaritalStatus],
    --         [Details_EthnicGroup],
    --         [Details_HighestLevelOfEducation],
    --         [Details_EmploymentStatus],
    --         [Details_GrossIncome]
    --     FROM [BetterLifeReportingData_Prod].[dbo].[covermorelead3]'
    --)
    SELECT
        GETDATE()                AS Lead_LeadDate,
        GETDATE()                AS Lead_ReadyForProcessingDate,
        p.Lead_Product,
        nr.IDNumber,
        nr.Title,
        nr.Initials,
        nr.FirstName,
        nr.Surname,
        nr.DateOfBirth,
        nr.CellPhoneNumber,
        nr.WorkPhoneNumber,
        nr.HomePhoneNumber,
        nr.EmailAddress,
        nr.ApplicationReference,
        nr.PurchasePrice,
        nr.LoanAmount,
        nr.Bank,
        nr.BondAttorney,
        nr.Region,
        nr.ConsultantName,
        nr.ConsultantId,
        nr.PropertyDescription,
        nr.AddressLine1,
        nr.AddressLine2,
        nr.City,
        nr.Suburb,
        nr.Province,
        nr.PostalCode,
        nr.IsNewDevelopment,
        nr.LoanType,
        nr.PropertyType,
        nr.BusinessEntity,
        nr.Gender,
        nr.HomeLanguage,
        nr.MaritalStatus,
        nr.EthnicGroup,
        nr.HighestLevelOfEducation,
        nr.EmploymentStatus,
        nr.GrossIncome
    FROM NumberedRecords nr
    CROSS APPLY ( VALUES (1),(6) ) AS p(Lead_Product)
    WHERE NOT EXISTS (
        SELECT 1
          FROM OPENQUERY(
               [HO-VSSQL-SVR01],
               'SELECT [Details_IDNumber],[Lead_Product] 
                  FROM [BetterLifeReportingData_Prod].[dbo].[covermorelead3]'
          ) existing
         WHERE existing.Details_IDNumber = nr.IDNumber
           AND existing.Lead_Product     = p.Lead_Product
    );

    IF @@ROWCOUNT = 0
        BREAK;

    SET @Offset += @BatchSize;
END;

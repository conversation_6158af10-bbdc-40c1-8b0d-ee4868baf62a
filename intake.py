import pandas as pd

# 1) Load the data
file_path = r"C:\Users\<USER>\OneDrive - Betterhome Group Ltd\Documents\BS\Data and Analytics\Data\Intake\Intake.xlsx"
df = pd.read_excel(file_path)

# 2) Normalize column names (lowercase, strip spaces/underscores)
df.columns = (
    df.columns
      .str.lower()
      .str.replace(r'[\s_]+', '', regex=True)
)

# 3) Map your real column names → our standardized names
col_map = {
    'mainapplicantidnumber': 'IdNumber',
    'statusworkflow'       : 'StatusWorkflow',
    'leadid'               : 'LeadID',
    'product'             : 'LeadType',
    'processingcomment'    : 'LeadProcessComment',  # matches your Intake file
    'status'               : 'Status'
}
df = df.rename(columns=col_map)

# Sanity‐check that everything we need is there
expected = set(col_map.values())
missing  = expected - set(df.columns)
if missing:
    raise KeyError(f"Missing after rename: {missing}\nHave instead: {df.columns.tolist()}")

# 4) Define success flag
df['IsSuccessful'] = df['StatusWorkflow'] == 'Granted'

# 5) Build the “no‐lead reasons” helper
def collect_no_lead_reasons(grp):
    mask = grp['IsSuccessful'] & grp['LeadID'].isna()
    return (
        grp.loc[mask]
           .apply(lambda r: f"{r['LeadProcessComment']} ({r['Status']})", axis=1)
           .tolist()
    )

# 6) Aggregate per IdNumber
agg = df.groupby('IdNumber').agg(
    TotalSubmissions      = ('IdNumber',    'count'),
    SuccessfulSubmissions = ('IsSuccessful','sum'),
    LeadCreated           = ('LeadID',      lambda x: 'Yes' if x.notna().any() else 'No'),
    HomeOwnersCoverCount  = ('LeadType',    lambda x: (x == 'HomeOwnersCover').sum()),
    CreditLifeCount       = ('LeadType',    lambda x: (x == 'CreditLife').sum()),
    BetterSureHomeCount   = ('LeadType',    lambda x: (x == 'BetterSureHome').sum())
)

agg['NoLeadReasonList'] = df.groupby('IdNumber').apply(collect_no_lead_reasons)

# 7) Final summary DataFrame (ordered columns)
summary_df = agg.reset_index()[[
    'IdNumber',
    'TotalSubmissions',
    'SuccessfulSubmissions',
    'LeadCreated',
    'HomeOwnersCoverCount',
    'CreditLifeCount',
    'BetterSureHomeCount',
    'NoLeadReasonList'
]]

# 8) Export + display
summary_df.to_excel('analysis_summary.xlsx', index=False)
print(summary_df)
